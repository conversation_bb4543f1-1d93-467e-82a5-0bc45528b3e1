import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 0

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

# 黑色边框检测阈值 - 新的检测方案
black_threshold = (0, 50)  # 灰度阈值，用于检测黑色边框
current_black_threshold = black_threshold

# 激光笔控制相关变量
laser_pin = None  # 激光笔控制引脚
laser_enabled = False  # 激光笔状态

#矩形四点的坐标
rect_corner_points = []

#屏幕目标点坐标 - 修复：变量名错误
target_1_cx = 50
target_1_cy = 150  # 修复：原代码写成了target_1_cx

#屏幕中心
target_2_cx = 400
target_2_cy = 240

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)

# 新增：系统状态变量
last_target_time = 0  # 上次检测到目标的时间
target_lost_threshold = 1000  # 目标丢失阈值(ms)
stable_count = 0  # 稳定计数器
max_stable_count = 20  # 最大稳定计数

# 新增：性能优化变量
frame_skip_count = 0  # 跳帧计数
max_frame_skip = 2  # 最大跳帧数

# 新增：程序运行控制
program_running = True  # 程序运行标志

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 800, height = 480)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立触摸屏
tp = TOUCH(0)

print("系统初始化完成，当前Flag:", Flag)

#__________________________________________功能函数定义_____________________________________________

#找到对应最大色块 - 改进：增加面积和像素数量双重筛选
def find_max(blobs):
    if not blobs:
        return None
    
    max_size = 0
    max_blob = None
    for b in blobs:
        # 改进：综合考虑面积和像素数量
        blob_score = b[2] * b[3] + b.pixels() * 0.5
        if blob_score > max_size:
            max_blob = b
            max_size = blob_score
    return max_blob

#任务标志位改变函数 - 改进：增加错误处理
def Flag_transform():
    global Flag, threshold_adjustment_mode
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                new_flag = data[1]
                if 0 <= new_flag <= 7:  # 验证Flag范围
                    Flag = new_flag
                    print(f"Flag更新为: {Flag}")
                    
                    # 特殊处理：进入阈值调节模式
                    if Flag == 1:
                        threshold_adjustment_mode = True
                        print("进入阈值调节模式")
                else:
                    print(f"无效的Flag值: {new_flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

# 新增：检查触摸屏激活阈值调节模式 - 解决问题1
def check_touch_activation():
    """检查触摸屏是否激活阈值调节模式"""
    global threshold_adjustment_mode, touch_counter
    
    if Flag == 1 and not threshold_adjustment_mode:
        points = tp.read()
        if points:
            touch_counter += 1
            print(f"触摸计数: {touch_counter}")
            
            # 连续触摸3次激活阈值调节模式
            if touch_counter >= 3:
                threshold_adjustment_mode = True
                touch_counter = 0
                print("通过触摸激活阈值调节模式!")
                return True
            
            time.sleep_ms(200)  # 防止重复计数
        else:
            # 如果没有触摸，重置计数器（超时机制）
            if touch_counter > 0:
                touch_counter = max(0, touch_counter - 1)
    
    return False

# 新增：安全退出函数 - 解决问题2
def safe_exit():
    """安全退出程序的函数"""
    global program_running
    
    try:
        # 尝试正常的IDE退出点
        os.exitpoint()
    except:
        # 如果IDE不可用，检查其他退出条件
        try:
            # 检查是否有特殊的退出信号
            if uart.any():
                data = uart.read()
                if data and len(data) >= 3:
                    # 检查退出信号 0xFF 0xFF 0xFF
                    if data[0] == 0xFF and data[1] == 0xFF and data[2] == 0xFF:
                        print("收到退出信号，程序结束")
                        program_running = False
                        return True
        except:
            pass
    
    return False

#存储脱机调整的阈值 - 改进：增加验证和默认值处理
def storage():
    global current_red_threshold, current_blue_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            if len(saved_lab_thresholds) >= 2:
                current_blue_threshold = tuple(saved_lab_thresholds[-1])   # 最后一个作为蓝色
                current_red_threshold = tuple(saved_lab_thresholds[-2])    # 倒数第二个作为红色
            else:
                current_red_threshold = tuple(saved_lab_thresholds[0])     # 只有一个时作为红色
                current_blue_threshold = blue_threshold  # 使用默认蓝色阈值
        else:
            current_red_threshold = red_threshold  # 使用默认红色阈值
            current_blue_threshold = blue_threshold  # 使用默认蓝色阈值
        
        print(f"阈值已更新 - 红色: {current_red_threshold}, 蓝色: {current_blue_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")
        # 发生错误时使用默认值
        current_red_threshold = red_threshold
        current_blue_threshold = blue_threshold

# 新增：安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 新增：计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

# 新增：目标稳定性检查
def check_target_stability(target_x, target_y, center_x, center_y, threshold=20):
    """检查目标是否稳定在中心附近"""
    distance = calculate_distance(target_x, target_y, center_x, center_y)
    return distance < threshold

#Flag==2 - 简化版本，保持您的原始逻辑
def goto_target_point_1():
    """基础部分第二题：红色目标跟踪，蓝色目标出现时切换为相对跟踪"""
    global stable_count, last_target_time

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    max_red_blob = find_max(red_blobs)

    if not blue_blobs:
        if max_red_blob:
            last_target_time = ticks_ms()

            # 希望红色稳定在屏幕中心
            e_x_2 = 400 - max_red_blob.cx()
            e_y_2 = 240 - max_red_blob.cy()

            e_x_2_high = (e_x_2 >> 8) & 0xFF
            e_x_2_low = e_x_2 & 0xFF
            e_y_2_high = (e_y_2 >> 8) & 0xFF
            e_y_2_low = e_y_2 & 0xFF

            send_lst = [0x2c, 0x12, e_x_2_high, e_x_2_low, e_y_2_high, e_y_2_low, 0x5B]
            safe_uart_send(send_lst)

            # 检查稳定性
            if calculate_distance(max_red_blob.cx(), max_red_blob.cy(), 400, 240) < 20:
                stable_count += 1
                if stable_count >= max_stable_count:
                    n = 9 & 0xFF
                    send_lst = [0x2c, 0x12, n, 0x5B]
                    safe_uart_send(send_lst)
                    stable_count = 0
            else:
                stable_count = 0

            # 绘制检测结果
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)
            img.draw_string_advanced(10, 10, 16, f"跟踪红色 误差:X={e_x_2} Y={e_y_2}", color=(255,255,255))
    else:
        max_blue_blob = find_max(blue_blobs)
        if max_blue_blob and max_red_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            e_x_3 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_3 = max_red_blob.cy() - max_blue_blob.cy()

            e_x_3_high = (e_x_3 >> 8) & 0xFF
            e_x_3_low = e_x_3 & 0xFF
            e_y_3_high = (e_y_3 >> 8) & 0xFF
            e_y_3_low = e_y_3 & 0xFF

            send_lst = [0x2c, 0x12, e_x_3_high, e_x_3_low, e_y_3_high, e_y_3_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"相对跟踪 误差:X={e_x_3} Y={e_y_3}", color=(255,255,0))

    Display.show_image(img)

#Flag==3 - 简化版本，保持您的原始逻辑
def goto_target_point_2():
    """基础部分第三题：多目标切换跟踪"""
    global stable_count, last_target_time

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    max_red_blob = find_max(red_blobs)
    max_blue_blob = find_max(blue_blobs)

    if not blue_blobs:
        if not red_blobs:
            e_x_4 = target_1_cx - target_2_cx
            e_y_4 = target_1_cy - target_2_cy

            e_x_4_high = (e_x_4 >> 8) & 0xFF
            e_x_4_low = e_x_4 & 0xFF
            e_y_4_high = (e_y_4 >> 8) & 0xFF
            e_y_4_low = e_y_4 & 0xFF

            send_lst = [0x2c, 0x12, e_x_4_high, e_x_4_low, e_y_4_high, e_y_4_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, "移动到预设位置", color=(255,255,0))
        else:
            # 希望红色稳定在屏幕中心
            e_x_5 = 400 - max_red_blob.cx()
            e_y_5 = 240 - max_red_blob.cy()

            e_x_5_high = (e_x_5 >> 8) & 0xFF
            e_x_5_low = e_x_5 & 0xFF
            e_y_5_high = (e_y_5 >> 8) & 0xFF
            e_y_5_low = e_y_5 & 0xFF

            send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
            safe_uart_send(send_lst)

            if calculate_distance(max_red_blob.cx(), max_red_blob.cy(), 400, 240) < 20:
                stable_count += 1
                if stable_count >= 50:
                    n = 9 & 0xFF
                    send_lst = [0x2c, 0x12, n, 0x5B]
                    safe_uart_send(send_lst)
                    stable_count = 0

            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)
            img.draw_string_advanced(10, 10, 16, f"跟踪红色 误差:X={e_x_5} Y={e_y_5}", color=(0,255,0))
    else:
        if max_blue_blob and max_red_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            e_x_6 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_6 = max_red_blob.cy() - max_blue_blob.cy()

            e_x_6_high = (e_x_6 >> 8) & 0xFF
            e_x_6_low = e_x_6 & 0xFF
            e_y_6_high = (e_y_6 >> 8) & 0xFF
            e_y_6_low = e_y_6 & 0xFF

            send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"双目标相对跟踪 误差:X={e_x_6} Y={e_y_6}", color=(255,255,0))

    Display.show_image(img)

#Flag==4,5 - 简化版本
def goto_target_point_3():
    """发挥部分第一、二题：高精度双目标跟踪"""
    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    if red_blobs and blue_blobs:
        max_red_blob = find_max(red_blobs)
        max_blue_blob = find_max(blue_blobs)

        if max_red_blob and max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            e_x_7 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_7 = max_red_blob.cy() - max_blue_blob.cy()

            e_x_7_high = (e_x_7 >> 8) & 0xFF
            e_x_7_low = e_x_7 & 0xFF
            e_y_7_high = (e_y_7 >> 8) & 0xFF
            e_y_7_low = e_y_7 & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"高精度跟踪 误差:X={e_x_7} Y={e_y_7}", color=(255,255,255))

    Display.show_image(img)

#Flag==6 - 新增功能
def goto_target_point_4():
    """发挥部分第三题：多目标优先级跟踪"""
    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    # 优先跟踪红色目标
    if red_blobs:
        max_red_blob = find_max(red_blobs)
        img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=3)

        e_x = 400 - max_red_blob.cx()
        e_y = 240 - max_red_blob.cy()

        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF

        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)

        img.draw_string_advanced(10, 10, 16, f"优先跟踪红色 误差:X={e_x} Y={e_y}", color=(0,255,0))
    elif blue_blobs:
        max_blue_blob = find_max(blue_blobs)
        img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=3)

        e_x = 400 - max_blue_blob.cx()
        e_y = 240 - max_blue_blob.cy()

        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF

        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)

        img.draw_string_advanced(10, 10, 16, f"跟踪蓝色 误差:X={e_x} Y={e_y}", color=(255,255,0))
    else:
        img.draw_string_advanced(10, 10, 20, "未检测到目标", color=(255,0,0))

    Display.show_image(img)

#Flag==7 - 系统监控
def goto_target_point_5():
    """其他加分项：系统状态监控"""
    img = sensor.snapshot()

    # 显示系统信息
    fps = clock.fps()
    gc.collect()
    free_mem = gc.mem_free()

    img.draw_rectangle(10, 10, 300, 150, color=(50,50,50), thickness=2, fill=True)
    img.draw_string_advanced(20, 20, 20, "系统监控", color=(255,255,255))
    img.draw_string_advanced(20, 50, 16, f"FPS: {fps:.1f}", color=(0,255,0))
    img.draw_string_advanced(20, 70, 16, f"内存: {free_mem//1024}KB", color=(255,255,0))
    img.draw_string_advanced(20, 90, 16, f"Flag: {Flag}", color=(255,255,255))
    img.draw_string_advanced(20, 110, 14, f"红色阈值: {current_red_threshold}", color=(255,0,0))
    img.draw_string_advanced(20, 130, 14, f"蓝色阈值: {current_blue_threshold}", color=(0,0,255))

    Display.show_image(img)

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 简化的阈值调整系统
def threshold_adjustment_system():
    """简化的脱机调阈值系统"""
    global current_mode, threshold_adjustment_mode, Flag

    try:
        img_cam = sensor.snapshot()
        img_cam = img_cam.copy(roi=cut_roi)
        img = sensor.snapshot()

        # 应用当前阈值
        if current_mode == 'gray':
            img_processed = img_cam.to_grayscale()
            img_processed = img_processed.binary([gray_threshold[:2]])
            img_processed = img_processed.to_rgb565()
        else:  # 'lab'
            try:
                img_processed = img_cam.binary([lab_threshold])
                img_processed = img_processed.to_rgb565()
            except:
                img_processed = img_cam

        # 绘制处理后的图像
        center_x = (800 - img_processed.width()) // 2
        center_y = (480 - img_processed.height()) // 2
        img.draw_image(img_processed, center_x, center_y)

        # 绘制简化的控制界面
        button_color = (150, 150, 150)
        text_color = (0, 0, 0)

        # 基本按钮
        img.draw_rectangle(0, 0, 120, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(30, 10, 18, "返回", color=text_color)

        img.draw_rectangle(680, 0, 120, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(710, 10, 18, "切换", color=text_color)

        img.draw_rectangle(0, 440, 120, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(30, 450, 18, "保存", color=text_color)

        # 显示当前模式和阈值
        mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
        img.draw_string_advanced(300, 15, 20, f"模式: {mode_text}", color=(255,255,255))

        if current_mode == 'gray':
            img.draw_string_advanced(10, 400, 16, f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]", color=(255,255,255))
        else:
            img.draw_string_advanced(10, 400, 14, f"LAB: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]", color=(255,255,255))

        # 处理触摸输入
        points = tp.read()
        if points:
            x, y = points[0].x, points[0].y

            if x < 120 and y < 40:  # 返回按钮
                threshold_adjustment_mode = False
                Flag = 0
                print("退出阈值调节模式")
            elif x > 680 and y < 40:  # 切换按钮
                current_mode = 'gray' if current_mode == 'lab' else 'lab'
                print(f"切换到{current_mode}模式")
            elif x < 120 and y > 440:  # 保存按钮
                if current_mode == 'lab':
                    saved_lab_thresholds.append(lab_threshold.copy())
                    print(f"已保存LAB阈值: {lab_threshold}")
                else:
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            time.sleep_ms(200)

        return img

    except Exception as e:
        print(f"阈值调整系统错误: {e}")
        img = sensor.snapshot()
        img.draw_string_advanced(10, 10, 20, f"系统错误: {str(e)[:30]}", color=(255,0,0))
        return img

#_____________________________________________主程序________________________________________________
print("开始运行主程序...")

while program_running:
    try:
        clock.tick()

        # 安全退出检查 - 解决问题2
        if safe_exit():
            break

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 主要功能逻辑
        if Flag == 0:
            # 正常拍照模式
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 16, f"FPS: {clock.fps():.1f}", color=(0,255,0))
            img.draw_string_advanced(10, 30, 16, f"模式: 正常拍照", color=(255,255,255))
            img.draw_string_advanced(10, 450, 16, "发送串口指令切换模式", color=(255,255,0))
            Display.show_image(img)

        elif Flag == 1:
            # 脱机调阈值模式 - 解决问题1
            check_touch_activation()  # 检查触摸激活

            if threshold_adjustment_mode:
                img = threshold_adjustment_system()
                storage()
                Display.show_image(img)
            else:
                # 显示触摸激活提示
                img = sensor.snapshot()
                img.draw_string_advanced(200, 200, 24, "阈值调节模式", color=(255,255,0))
                img.draw_string_advanced(150, 240, 20, f"请连续触摸屏幕3次激活", color=(255,255,255))
                img.draw_string_advanced(200, 270, 20, f"当前: {touch_counter}/3", color=(0,255,0))

                # 绘制触摸区域提示
                img.draw_rectangle(200, 320, 400, 100, color=(100,100,100), thickness=2, fill=False)
                img.draw_string_advanced(350, 360, 20, "触摸区域", color=(255,255,255))

                Display.show_image(img)

        elif Flag == 2:
            goto_target_point_1()
        elif Flag == 3:
            goto_target_point_2()
        elif Flag == 4:
            goto_target_point_3()
        elif Flag == 5:
            goto_target_point_3()
        elif Flag == 6:
            goto_target_point_4()
        elif Flag == 7:
            goto_target_point_5()
        else:
            # 未知Flag值处理
            img = sensor.snapshot()
            img.draw_string_advanced(200, 200, 24, f"未知模式: Flag={Flag}", color=(255,0,0))
            img.draw_string_advanced(200, 240, 20, "请检查串口指令", color=(255,255,255))
            Display.show_image(img)

        # 内存管理
        if ticks_ms() % 5000 < 50:
            gc.collect()

    except Exception as e:
        print(f"主程序错误: {e}")
        try:
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 20, f"系统错误: {str(e)[:40]}", color=(255,0,0))
            img.draw_string_advanced(10, 40, 16, "系统将继续运行...", color=(255,255,0))
            Display.show_image(img)
        except:
            pass
        time.sleep_ms(100)

print("程序已安全退出")
