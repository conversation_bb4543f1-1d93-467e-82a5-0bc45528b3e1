import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 0

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值 - 改进：增加更精确的阈值
red_threshold = (92, 100, -22, 30, -29, 21)
blue_threshold = (30, 50, -50, -15, -10, 30)
current_red_threshold = red_threshold  # 修复：初始化为默认值
current_blue_threshold = blue_threshold  # 修复：初始化为默认值

#矩形四点的坐标
rect_corner_points = []

#屏幕目标点坐标 - 修复：变量名错误
target_1_cx = 50
target_1_cy = 150  # 修复：原代码写成了target_1_cx

#屏幕中心
target_2_cx = 400
target_2_cy = 240

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)

# 新增：系统状态变量
last_target_time = 0  # 上次检测到目标的时间
target_lost_threshold = 1000  # 目标丢失阈值(ms)
stable_count = 0  # 稳定计数器
max_stable_count = 20  # 最大稳定计数

# 新增：性能优化变量
frame_skip_count = 0  # 跳帧计数
max_frame_skip = 2  # 最大跳帧数

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 800, height = 480)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立触摸屏
tp = TOUCH(0)

print("系统初始化完成，当前Flag:", Flag)

#__________________________________________功能函数定义_____________________________________________

#找到对应最大色块 - 改进：增加面积和像素数量双重筛选
def find_max(blobs):
    if not blobs:
        return None
    
    max_size = 0
    max_blob = None
    for b in blobs:
        # 改进：综合考虑面积和像素数量
        blob_score = b[2] * b[3] + b.pixels() * 0.5
        if blob_score > max_size:
            max_blob = b
            max_size = blob_score
    return max_blob

#任务标志位改变函数 - 改进：增加错误处理
def Flag_transform():
    global Flag, threshold_adjustment_mode
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                new_flag = data[1]
                if 0 <= new_flag <= 7:  # 验证Flag范围
                    Flag = new_flag
                    print(f"Flag更新为: {Flag}")

                    # 特殊处理：进入阈值调节模式
                    if Flag == 1:
                        threshold_adjustment_mode = True
                        print("进入阈值调节模式")
                else:
                    print(f"无效的Flag值: {new_flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

# 新增：检查触摸屏激活阈值调节模式
def check_touch_activation():
    """检查触摸屏是否激活阈值调节模式"""
    global threshold_adjustment_mode, touch_counter

    if Flag == 1 and not threshold_adjustment_mode:
        points = tp.read()
        if points:
            touch_counter += 1
            print(f"触摸计数: {touch_counter}")

            # 连续触摸3次激活阈值调节模式
            if touch_counter >= 3:
                threshold_adjustment_mode = True
                touch_counter = 0
                print("通过触摸激活阈值调节模式!")
                return True

            time.sleep_ms(200)  # 防止重复计数
        else:
            # 如果没有触摸，重置计数器（超时机制）
            if touch_counter > 0:
                touch_counter = max(0, touch_counter - 1)

    return False

# 新增：安全退出函数
def safe_exit():
    """安全退出程序的函数"""
    try:
        # 尝试正常的IDE退出点
        os.exitpoint()
    except:
        # 如果IDE不可用，检查其他退出条件
        try:
            # 检查是否有特殊的退出信号
            if uart.any():
                data = uart.read()
                if data and len(data) >= 3:
                    # 检查退出信号 0xFF 0xFF 0xFF
                    if data[0] == 0xFF and data[1] == 0xFF and data[2] == 0xFF:
                        print("收到退出信号，程序结束")
                        return True
        except:
            pass

    return False

#存储脱机调整的阈值 - 改进：增加验证和默认值处理
def storage():
    global current_red_threshold, current_blue_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            if len(saved_lab_thresholds) >= 2:
                current_blue_threshold = tuple(saved_lab_thresholds[-1])   # 最后一个作为蓝色
                current_red_threshold = tuple(saved_lab_thresholds[-2])    # 倒数第二个作为红色
            else:
                current_red_threshold = tuple(saved_lab_thresholds[0])     # 只有一个时作为红色
                current_blue_threshold = blue_threshold  # 使用默认蓝色阈值
        else:
            current_red_threshold = red_threshold  # 使用默认红色阈值
            current_blue_threshold = blue_threshold  # 使用默认蓝色阈值
        
        print(f"阈值已更新 - 红色: {current_red_threshold}, 蓝色: {current_blue_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")
        # 发生错误时使用默认值
        current_red_threshold = red_threshold
        current_blue_threshold = blue_threshold

# 新增：安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 新增：计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

# 新增：目标稳定性检查
def check_target_stability(target_x, target_y, center_x, center_y, threshold=20):
    """检查目标是否稳定在中心附近"""
    distance = calculate_distance(target_x, target_y, center_x, center_y)
    return distance < threshold

#Flag==2 - 改进版本
def goto_target_point_1():
    """基础部分第二题：红色目标跟踪，蓝色目标出现时切换为相对跟踪"""
    global stable_count, last_target_time

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    max_red_blob = find_max(red_blobs)

    # 绘制检测结果
    if max_red_blob:
        img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)
        img.draw_cross(max_red_blob.cx(), max_red_blob.cy(), color=(0,255,0), size=10, thickness=2)

    if not blue_blobs:
        if max_red_blob:
            last_target_time = ticks_ms()

            # 计算误差
            e_x_2 = 400 - max_red_blob.cx()
            e_y_2 = 240 - max_red_blob.cy()

            # 检查目标稳定性
            if check_target_stability(max_red_blob.cx(), max_red_blob.cy(), 400, 240):
                stable_count += 1
                img.draw_string_advanced(10, 10, 20, f"稳定计数: {stable_count}/{max_stable_count}", color=(0,255,0))

                if stable_count >= max_stable_count:
                    # 发送停止信号
                    send_lst = [0x2c, 0x12, 0x00, 0x09, 0x5B]
                    safe_uart_send(send_lst)
                    img.draw_string_advanced(10, 40, 20, "目标已稳定!", color=(0,255,0))
                    stable_count = 0  # 重置计数器
                    Display.show_image(img)
                    return
            else:
                stable_count = 0

            # 发送控制信号
            e_x_2_high = (e_x_2 >> 8) & 0xFF
            e_x_2_low = e_x_2 & 0xFF
            e_y_2_high = (e_y_2 >> 8) & 0xFF
            e_y_2_low = e_y_2 & 0xFF

            send_lst = [0x2c, 0x12, e_x_2_high, e_x_2_low, e_y_2_high, e_y_2_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示误差信息
            img.draw_string_advanced(10, 70, 16, f"误差: X={e_x_2}, Y={e_y_2}", color=(255,255,255))
        else:
            img.draw_string_advanced(10, 10, 20, "未检测到红色目标", color=(255,0,0))
    else:
        max_blue_blob = find_max(blue_blobs)
        if max_blue_blob and max_red_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            # 相对跟踪模式
            e_x_3 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_3 = max_red_blob.cy() - max_blue_blob.cy()

            e_x_3_high = (e_x_3 >> 8) & 0xFF
            e_x_3_low = e_x_3 & 0xFF
            e_y_3_high = (e_y_3 >> 8) & 0xFF
            e_y_3_low = e_y_3 & 0xFF

            send_lst = [0x2c, 0x12, e_x_3_high, e_x_3_low, e_y_3_high, e_y_3_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, "相对跟踪模式", color=(255,255,0))
            img.draw_string_advanced(10, 30, 16, f"相对误差: X={e_x_3}, Y={e_y_3}", color=(255,255,255))

    Display.show_image(img)

#Flag==3 - 改进版本
def goto_target_point_2():
    """基础部分第三题：多目标切换跟踪"""
    global stable_count, last_target_time

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    max_red_blob = find_max(red_blobs)
    max_blue_blob = find_max(blue_blobs)

    if not blue_blobs:
        if not red_blobs:
            # 都没有检测到，移动到预设位置
            e_x_4 = target_1_cx - target_2_cx
            e_y_4 = target_1_cy - target_2_cy

            e_x_4_high = (e_x_4 >> 8) & 0xFF
            e_x_4_low = e_x_4 & 0xFF
            e_y_4_high = (e_y_4 >> 8) & 0xFF
            e_y_4_low = e_y_4 & 0xFF

            send_lst = [0x2c, 0x12, e_x_4_high, e_x_4_low, e_y_4_high, e_y_4_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 20, "移动到预设位置", color=(255,255,0))
        else:
            # 只有红色目标，跟踪红色
            last_target_time = ticks_ms()

            e_x_5 = 400 - max_red_blob.cx()
            e_y_5 = 240 - max_red_blob.cy()

            # 检查稳定性
            if check_target_stability(max_red_blob.cx(), max_red_blob.cy(), 400, 240):
                stable_count += 1
                if stable_count >= max_stable_count:
                    send_lst = [0x2c, 0x12, 0x00, 0x09, 0x5B]
                    safe_uart_send(send_lst)
                    stable_count = 0
                    img.draw_string_advanced(10, 40, 20, "红色目标已稳定!", color=(0,255,0))
                    Display.show_image(img)
                    return
            else:
                stable_count = 0

            e_x_5_high = (e_x_5 >> 8) & 0xFF
            e_x_5_low = e_x_5 & 0xFF
            e_y_5_high = (e_y_5 >> 8) & 0xFF
            e_y_5_low = e_y_5 & 0xFF

            send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)
            img.draw_string_advanced(10, 10, 16, "跟踪红色目标", color=(0,255,0))
    else:
        # 有蓝色目标，进入相对跟踪模式
        if max_blue_blob and max_red_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            e_x_6 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_6 = max_red_blob.cy() - max_blue_blob.cy()

            e_x_6_high = (e_x_6 >> 8) & 0xFF
            e_x_6_low = e_x_6 & 0xFF
            e_y_6_high = (e_y_6 >> 8) & 0xFF
            e_y_6_low = e_y_6 & 0xFF

            send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255,255,0))

    Display.show_image(img)

#Flag==4,5 - 改进版本
def goto_target_point_3():
    """发挥部分第一、二题：高精度双目标跟踪"""
    global last_target_time

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    if red_blobs and blue_blobs:
        max_red_blob = find_max(red_blobs)
        max_blue_blob = find_max(blue_blobs)

        if max_red_blob and max_blue_blob:
            last_target_time = ticks_ms()

            # 绘制检测结果
            img.draw_rectangle(max_blue_blob.rect(), color=(255,255,0), thickness=2)
            img.draw_rectangle(max_red_blob.rect(), color=(0,255,0), thickness=2)

            # 绘制连线
            img.draw_line(max_red_blob.cx(), max_red_blob.cy(),
                         max_blue_blob.cx(), max_blue_blob.cy(),
                         color=(255,255,255), thickness=2)

            # 计算相对位置误差
            e_x_7 = max_red_blob.cx() - max_blue_blob.cx()
            e_y_7 = max_red_blob.cy() - max_blue_blob.cy()

            # 计算距离和角度信息
            distance = calculate_distance(max_red_blob.cx(), max_red_blob.cy(),
                                        max_blue_blob.cx(), max_blue_blob.cy())

            e_x_7_high = (e_x_7 >> 8) & 0xFF
            e_x_7_low = e_x_7 & 0xFF
            e_y_7_high = (e_y_7 >> 8) & 0xFF
            e_y_7_low = e_y_7 & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示详细信息
            img.draw_string_advanced(10, 10, 16, f"双目标距离: {distance:.1f}", color=(255,255,255))
            img.draw_string_advanced(10, 30, 16, f"相对误差: X={e_x_7}, Y={e_y_7}", color=(255,255,255))
            img.draw_string_advanced(10, 50, 16, f"红色: ({max_red_blob.cx()},{max_red_blob.cy()})", color=(0,255,0))
            img.draw_string_advanced(10, 70, 16, f"蓝色: ({max_blue_blob.cx()},{max_blue_blob.cy()})", color=(255,255,0))
        else:
            img.draw_string_advanced(10, 10, 20, "目标检测失败", color=(255,0,0))
    else:
        # 目标丢失处理
        current_time = ticks_ms()
        if current_time - last_target_time > target_lost_threshold:
            img.draw_string_advanced(10, 10, 20, "目标丢失，搜索中...", color=(255,0,0))
            # 可以添加搜索模式的代码
        else:
            img.draw_string_advanced(10, 10, 20, "等待目标出现...", color=(255,255,0))

    Display.show_image(img)

#Flag==6 - 新增功能
def goto_target_point_4():
    """发挥部分第三题：高级功能 - 多目标识别与优先级跟踪"""
    global last_target_time, rect_corner_points

    img = sensor.snapshot()
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    # 多目标检测与排序
    all_targets = []

    # 添加红色目标
    for blob in red_blobs:
        if blob.pixels() > 20:  # 过滤小目标
            all_targets.append({
                'blob': blob,
                'color': 'red',
                'priority': 1,  # 红色优先级高
                'area': blob.pixels()
            })

    # 添加蓝色目标
    for blob in blue_blobs:
        if blob.pixels() > 20:
            all_targets.append({
                'blob': blob,
                'color': 'blue',
                'priority': 2,  # 蓝色优先级低
                'area': blob.pixels()
            })

    if all_targets:
        # 按优先级和面积排序
        all_targets.sort(key=lambda x: (x['priority'], -x['area']))

        # 选择最高优先级目标
        primary_target = all_targets[0]
        target_blob = primary_target['blob']

        # 绘制所有目标
        for i, target in enumerate(all_targets):
            blob = target['blob']
            color = (0,255,0) if target['color'] == 'red' else (255,255,0)
            thickness = 3 if i == 0 else 1  # 主目标粗线

            img.draw_rectangle(blob.rect(), color=color, thickness=thickness)
            img.draw_string_advanced(blob.cx()-10, blob.cy()-20, 14,
                                   f"{target['color'][:1].upper()}{i+1}", color=color)

        # 跟踪主目标
        e_x = 400 - target_blob.cx()
        e_y = 240 - target_blob.cy()

        e_x_high = (e_x >> 8) & 0xFF
        e_x_low = e_x & 0xFF
        e_y_high = (e_y >> 8) & 0xFF
        e_y_low = e_y & 0xFF

        send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
        safe_uart_send(send_lst)

        # 显示信息
        img.draw_string_advanced(10, 10, 16, f"主目标: {primary_target['color']}", color=(255,255,255))
        img.draw_string_advanced(10, 30, 16, f"总目标数: {len(all_targets)}", color=(255,255,255))
        img.draw_string_advanced(10, 50, 16, f"误差: X={e_x}, Y={e_y}", color=(255,255,255))

        last_target_time = ticks_ms()
    else:
        img.draw_string_advanced(10, 10, 20, "未检测到任何目标", color=(255,0,0))

    Display.show_image(img)

#Flag==7 - 其他功能
def goto_target_point_5():
    """其他加分项：系统状态监控与性能显示"""
    global frame_skip_count, last_target_time

    img = sensor.snapshot()

    # 性能监控
    fps = clock.fps()
    current_time = ticks_ms()

    # 内存使用情况
    gc.collect()
    free_mem = gc.mem_free()
    alloc_mem = gc.mem_alloc()

    # 系统状态显示
    img.draw_rectangle(10, 10, 300, 200, color=(50,50,50), thickness=2, fill=True)

    # 标题
    img.draw_string_advanced(20, 20, 20, "系统状态监控", color=(255,255,255))

    # FPS信息
    img.draw_string_advanced(20, 50, 16, f"FPS: {fps:.1f}", color=(0,255,0))

    # 内存信息
    img.draw_string_advanced(20, 70, 16, f"空闲内存: {free_mem//1024}KB", color=(255,255,0))
    img.draw_string_advanced(20, 90, 16, f"已用内存: {alloc_mem//1024}KB", color=(255,100,100))

    # 当前模式
    img.draw_string_advanced(20, 110, 16, f"当前Flag: {Flag}", color=(255,255,255))

    # 阈值信息
    img.draw_string_advanced(20, 130, 14, f"红色阈值: {current_red_threshold}", color=(255,0,0))
    img.draw_string_advanced(20, 150, 14, f"蓝色阈值: {current_blue_threshold}", color=(0,0,255))

    # 目标丢失时间
    time_since_target = current_time - last_target_time
    img.draw_string_advanced(20, 170, 16, f"目标丢失: {time_since_target}ms", color=(255,255,0))

    # 简单的目标检测（用于状态显示）
    red_blobs = img.find_blobs([current_red_threshold], pixels_threshold=10, area_threshold=10)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    img.draw_string_advanced(20, 190, 16, f"检测目标: R{len(red_blobs)} B{len(blue_blobs)}", color=(255,255,255))

    Display.show_image(img)

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数 - 保持原有逻辑
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        # 检查L_min <= L_max
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        # 检查A_min <= A_max
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        # 检查B_min <= B_max
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        # 检查gray_min <= gray_max
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数 - 改进版本，增加错误处理
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag

    try:
        # 捕获摄像头图像
        img_cam = sensor.snapshot()
        img_cam = img_cam.copy(roi=cut_roi)

        # 创建用于绘制按钮的画布
        img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

        # 应用当前阈值到裁剪的图像
        if current_mode == 'gray':
            # 灰度图识别 - 二值化显示
            img_processed = img_cam.to_grayscale()
            img_processed = img_processed.binary([gray_threshold[:2]])
            img_processed = img_processed.to_rgb565()
        else:  # 'lab'
            # LAB空间识别 - 二值化显示
            try:
                img_processed = img_cam.binary([lab_threshold])
                img_processed = img_processed.to_rgb565()
            except:
                img_processed = img_cam

        # 将处理后的图像绘制到中央
        center_x = (800 - img_processed.width()) // 2
        center_y = (480 - img_processed.height()) // 2
        img.draw_image(img_processed, center_x, center_y)

        # 绘制界面按钮
        button_color = (150, 150, 150)
        text_color = (0, 0, 0)

        # 返回按钮
        img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, 10, 20, "返回", color=text_color)

        # 切换按钮
        img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(800-160+50, 10, 20, "切换", color=text_color)

        # 归位按钮
        img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, 480-30, 20, "归位", color=text_color)

        # 保存按钮
        img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
        img.draw_string_advanced(800-160+50, 480-30, 20, "保存", color=text_color)

        # 左侧滑块按钮（减少值）
        button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
        for i in range(6):
            y_pos = 60 + i * 60
            img.draw_rectangle(0, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
            if button_labels_left[i]:  # 只显示有效按钮的标签
                img.draw_string_advanced(70, y_pos + 10, 20, button_labels_left[i], color=text_color)

        # 右侧滑块按钮（增加值）
        button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
        for i in range(6):
            y_pos = 60 + i * 60
            img.draw_rectangle(800-160, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
            if button_labels_right[i]:  # 只显示有效按钮的标签
                img.draw_string_advanced(800-160+70, y_pos + 10, 20, button_labels_right[i], color=text_color)

        # 显示当前模式提示
        img.draw_rectangle(300, 10, 200, 30, color=(200, 200, 200), thickness=2, fill=True)
        mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
        img.draw_string_advanced(320, 15, 20, f"模式: {mode_text}", color=text_color)

        # 显示当前阈值值
        if current_mode == 'gray':
            img.draw_string_advanced(10, 420, 18,
                                     f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                     color=text_color)
        else:
            img.draw_string_advanced(10, 420, 16,
                                     f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                     color=text_color)

        # 在中间图像下面显示已保存的阈值
        img_center_x = 400  # 屏幕中心X坐标
        saved_start_y = 320  # 保存阈值显示的起始Y坐标

        # 显示已保存阈值标题
        img.draw_string_advanced(img_center_x - 60, saved_start_y, 18, "已保存阈值:", color=text_color)

        # 显示各模式的保存阈值
        y_offset = 25
        if saved_lab_thresholds:
            img.draw_string_advanced(img_center_x - 80, saved_start_y + y_offset, 16,
                                   f"LAB模式({len(saved_lab_thresholds)}组):", color=text_color)
            y_offset += 20

            # 显示最近保存的2组阈值
            for i, threshold_val in enumerate(saved_lab_thresholds[-2:]):
                threshold_str = str(threshold_val)
                if len(threshold_str) > 35:
                    threshold_str = threshold_str[:35] + "..."
                img.draw_string_advanced(img_center_x - 100, saved_start_y + y_offset, 14,
                                       f"  {i+1}: {threshold_str}", color=text_color)
                y_offset += 18
            y_offset += 5

        if saved_gray_thresholds:
            img.draw_string_advanced(img_center_x - 80, saved_start_y + y_offset, 16,
                                   f"灰度模式({len(saved_gray_thresholds)}组):", color=text_color)
            y_offset += 20

            # 显示最近保存的2组阈值
            for i, threshold_val in enumerate(saved_gray_thresholds[-2:]):
                img.draw_string_advanced(img_center_x - 100, saved_start_y + y_offset, 14,
                                       f"  {i+1}: {threshold_val}", color=text_color)
                y_offset += 18

        # 处理触摸输入
        points = tp.read()
        if points:
            x, y = points[0].x, points[0].y

            # 判断按下的按钮
            def which_key(x, y):
                if x < 160:
                    if y < 40: return "return"
                    if y > 480-40: return "reset"
                    if 60 <= y < 420: return str((y-60)//60)
                elif x > 800-160:
                    if y < 40: return "change"
                    if y > 480-40: return "save"
                    if 60 <= y < 420: return str((y-60)//60+6)
                return None

            btn = which_key(x, y)
            if btn:
                # 返回按钮
                if btn == "return":
                    threshold_adjustment_mode = False
                    Flag = 0  # 改进：返回到正常拍照模式
                    print("退出阈值调节模式，Flag重置为0")
                    return img

                # 切换阈值模式
                elif btn == "change":
                    if current_mode == 'lab':
                        current_mode = 'gray'
                        print("切换到灰度模式")
                    else:
                        current_mode = 'lab'
                        print("切换到LAB模式")

                # 阈值归位
                elif btn == "reset":
                    if current_mode == 'gray':
                        gray_threshold[0] = 0
                        gray_threshold[1] = 255
                        print("已重置灰度阈值为全白色背景")
                    else:  # lab
                        lab_threshold[0] = 0
                        lab_threshold[1] = 100
                        lab_threshold[2] = -128
                        lab_threshold[3] = 127
                        lab_threshold[4] = -128
                        lab_threshold[5] = 127
                        print("已重置LAB阈值为全白色背景")

                # 保存当前阈值
                elif btn == "save":
                    if current_mode == 'lab':
                        # LAB模式保存时需要调整A、B通道的值（减去128）
                        adj_vals = [lab_threshold[i] for i in range(6)]
                        saved_lab_thresholds.append(adj_vals.copy())
                        print(f"已保存LAB阈值: {adj_vals}")
                    elif current_mode == 'gray':
                        saved_gray_thresholds.append(gray_threshold[:2].copy())
                        print(f"已保存灰度阈值: {gray_threshold[:2]}")

                # 调整滑块
                else:
                    idx = int(btn)
                    max_params = 2 if current_mode == 'gray' else 6

                    if idx >= 6:  # 右侧按钮增加阈值
                        chan_idx = idx - 6
                        if chan_idx < max_params:
                            if current_mode == 'gray':
                                gray_threshold[chan_idx] = min(255, gray_threshold[chan_idx] + 5)
                            else:  # lab
                                if chan_idx < 2:  # L通道
                                    lab_threshold[chan_idx] = min(100, lab_threshold[chan_idx] + 2)
                                else:  # A, B通道
                                    lab_threshold[chan_idx] = min(127, lab_threshold[chan_idx] + 2)
                    else:  # 左侧按钮减小阈值
                        chan_idx = idx
                        if chan_idx < max_params:
                            if current_mode == 'gray':
                                gray_threshold[chan_idx] = max(0, gray_threshold[chan_idx] - 5)
                            else:  # lab
                                if chan_idx < 2:  # L通道
                                    lab_threshold[chan_idx] = max(0, lab_threshold[chan_idx] - 2)
                                else:  # A, B通道
                                    lab_threshold[chan_idx] = max(-128, lab_threshold[chan_idx] - 2)

                    # 验证阈值逻辑
                    validate_threshold()

                time.sleep_ms(100)  # 防止重复触发

        return img

    except Exception as e:
        print(f"阈值调整系统错误: {e}")
        # 发生错误时返回普通图像
        img = sensor.snapshot()
        img.draw_string_advanced(10, 10, 20, f"系统错误: {str(e)[:30]}", color=(255,0,0))
        return img

#_____________________________________________主程序________________________________________________
print("开始运行主程序...")

while True:
    try:
        clock.tick()

        # 安全退出检查 - 解决问题2
        if safe_exit():
            break

        # 串口数据处理
        if uart.any():
            Flag_transform()

        # 性能优化：跳帧处理
        frame_skip_count += 1
        if frame_skip_count <= max_frame_skip and Flag not in [1, 7]:  # 阈值调节和监控模式不跳帧
            continue
        frame_skip_count = 0

        # 主要功能逻辑
        if Flag == 0:
            # 正常拍照模式
            img = sensor.snapshot()

            # 显示基本信息
            img.draw_string_advanced(10, 10, 16, f"FPS: {clock.fps():.1f}", color=(0,255,0))
            img.draw_string_advanced(10, 30, 16, f"模式: 正常拍照", color=(255,255,255))
            img.draw_string_advanced(10, 450, 16, "发送串口指令切换模式", color=(255,255,0))

            Display.show_image(img) #显示图片

        elif Flag == 1:
            # 脱机调阈值模式 - 解决问题1

            # 检查触摸激活 - 新增功能
            check_touch_activation()

            if threshold_adjustment_mode:
                # 执行阈值调节系统
                img = threshold_adjustment_system()
                storage()  # 修复：函数调用语法错误
                Display.show_image(img)
            else:
                # 如果没有进入阈值调节模式，显示提示并等待触摸
                img = sensor.snapshot()
                img.draw_string_advanced(150, 180, 24, "阈值调节模式", color=(255,255,0))
                img.draw_string_advanced(150, 220, 20, f"请连续触摸屏幕3次激活", color=(255,255,255))
                img.draw_string_advanced(150, 250, 20, f"当前触摸次数: {touch_counter}/3", color=(0,255,0))
                img.draw_string_advanced(150, 280, 18, "或发送串口指令直接激活", color=(200,200,200))

                # 绘制触摸提示区域
                img.draw_rectangle(200, 320, 400, 100, color=(100,100,100), thickness=2, fill=False)
                img.draw_string_advanced(320, 360, 20, "触摸此区域", color=(255,255,255))

                Display.show_image(img)

        #基础部分第二题
        elif Flag == 2:
            goto_target_point_1()

        #基础部分第三题
        elif Flag == 3:
            goto_target_point_2()

        #发挥部分第一题
        elif Flag == 4:
            goto_target_point_3()

        #发挥部分第二题
        elif Flag == 5:
            goto_target_point_3()

        #发挥部分第三题
        elif Flag == 6:
            goto_target_point_4()

        #其他加分项
        elif Flag == 7:
            goto_target_point_5()

        else:
            # 未知Flag值处理
            img = sensor.snapshot()
            img.draw_string_advanced(200, 200, 24, f"未知模式: Flag={Flag}", color=(255,0,0))
            img.draw_string_advanced(200, 240, 20, "请检查串口指令", color=(255,255,255))
            Display.show_image(img)

        # 内存管理
        if ticks_ms() % 5000 < 50:  # 每5秒执行一次垃圾回收
            gc.collect()

    except Exception as e:
        print(f"主程序错误: {e}")
        # 错误处理：显示错误信息并继续运行
        try:
            img = sensor.snapshot()
            img.draw_string_advanced(10, 10, 20, f"系统错误: {str(e)[:40]}", color=(255,0,0))
            img.draw_string_advanced(10, 40, 16, "系统将继续运行...", color=(255,255,0))
            Display.show_image(img)
        except:
            pass  # 如果连显示都失败，就静默继续

        time.sleep_ms(100)  # 短暂延迟后继续
