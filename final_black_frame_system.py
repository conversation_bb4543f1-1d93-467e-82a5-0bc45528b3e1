import time
import os
import sys
import math
import random
import gc

from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import UART
from machine import RTC
from machine import ADC
from machine import TOUCH

from media.sensor import *
from media.display import *
from media.media import *

#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 0

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值
red_threshold = (92, 100, -22, 30, -29, 21)
blue_threshold = (30, 50, -50, -15, -10, 30)

# 黑色边框检测阈值 - 新的检测方案
black_threshold = (0, 50)  # 灰度阈值，用于检测黑色边框
current_black_threshold = black_threshold

# 激光笔控制相关变量
laser_enabled = False  # 激光笔状态
laser_fire_time = 0  # 激光发射时间记录

#矩形检测相关变量
detected_rectangles = []  # 检测到的矩形列表
target_center_x = 0  # 目标中心X坐标
target_center_y = 0  # 目标中心Y坐标

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)

# 新增：系统状态变量
last_target_time = 0  # 上次检测到目标的时间
target_lost_threshold = 1000  # 目标丢失阈值(ms)
stable_count = 0  # 稳定计数器
max_stable_count = 10  # 最大稳定计数（0.5秒内判断）

# 新增：精确瞄准控制变量
precision_threshold = 5  # 精确瞄准阈值（5个像素点）
precision_stable_count = 0  # 精确稳定计数器
precision_check_interval = 50  # 检查间隔(ms)
precision_start_time = 0  # 精确检查开始时间

#_________________________________________模块初始化_____________________________________________

#摄像头传感器初始化
sensor = Sensor()
sensor.reset()
sensor.set_framesize(width = 800, height = 480)
sensor.set_pixformat(Sensor.RGB565)

#串口初始化
fpioa = FPIOA()
# UART1代码
fpioa.set_function(3,FPIOA.UART1_TXD)
fpioa.set_function(4,FPIOA.UART1_RXD)

uart=UART(UART.UART1,115200) #设置串口号1和波特率

fpioa.set_function(52,FPIOA.GPIO52)
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出

#缓冲区和3.5寸的屏幕图像显示
Display.init(Display.ST7701, to_ide=True)

# 初始化媒体管理器
MediaManager.init()

#启动摄像头传感器模块
sensor.run()

#时钟模块建立
clock = time.clock()

#建立触摸屏
tp = TOUCH(0)

print("黑色边框检测系统初始化完成，当前Flag:", Flag)

#_________________________________________原有功能函数定义_____________________________________________

#找到对应最大色块
def find_max(blobs):
    if not blobs:
        return None
    max_size = 0
    max_blob = None
    for b in blobs:
        if b[2]*b[3] > max_size:
            max_blob = b
            max_size = b[2]*b[3]
    return max_blob

#任务标志位改变函数
def Flag_transform():
    global Flag, threshold_adjustment_mode
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                Flag = data[1]
                print(f"Flag更新为: {Flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

#存储脱机调整的阈值
def storage():
    """更新当前使用的阈值"""
    global current_black_threshold
    try:
        # 使用保存的灰度阈值作为黑色检测阈值
        if saved_gray_thresholds:
            current_black_threshold = tuple(saved_gray_thresholds[-1])  # 使用最后保存的灰度阈值
            print(f"黑色检测阈值已更新为: {current_black_threshold}")
        else:
            current_black_threshold = black_threshold  # 使用默认阈值
            print("使用默认黑色检测阈值")
    except Exception as e:
        print(f"阈值存储错误: {e}")
        current_black_threshold = black_threshold

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

#__________________________________________黑色边框检测功能函数_____________________________________________

# 新增：黑色边框检测函数
def detect_black_rectangles(img):
    """检测黑色边框矩形"""
    global detected_rectangles, target_center_x, target_center_y
    
    try:
        # 转换为灰度图像
        gray_img = img.to_grayscale()
        
        # 二值化处理，检测黑色区域
        binary_img = gray_img.binary([current_black_threshold])
        
        # 查找轮廓/色块
        blobs = binary_img.find_blobs([(0, 255)], pixels_threshold=100, area_threshold=500, merge=True)
        
        rectangles = []
        for blob in blobs:
            # 过滤掉太小或太大的区域
            if 500 < blob.pixels() < 50000:
                # 计算矩形度（接近矩形的程度）
                rect_ratio = (blob.w() * blob.h()) / blob.pixels()
                if 1.2 < rect_ratio < 3.0:  # 矩形度筛选
                    rectangles.append({
                        'blob': blob,
                        'center_x': blob.cx(),
                        'center_y': blob.cy(),
                        'area': blob.pixels(),
                        'rect_ratio': rect_ratio
                    })
        
        # 按面积排序，寻找内外框
        rectangles.sort(key=lambda x: x['area'])
        detected_rectangles = rectangles
        
        # 如果检测到至少两个矩形，计算目标中心
        if len(rectangles) >= 2:
            # 假设最大的是外框，第二大的是内框（或相反）
            outer_rect = rectangles[-1]  # 最大的
            inner_rect = rectangles[-2]  # 第二大的
            
            # 计算两个矩形的中心点的平均值作为目标中心
            target_center_x = (outer_rect['center_x'] + inner_rect['center_x']) // 2
            target_center_y = (outer_rect['center_y'] + inner_rect['center_y']) // 2
            
            return True, target_center_x, target_center_y, rectangles
        
        return False, 0, 0, rectangles
        
    except Exception as e:
        print(f"黑色边框检测错误: {e}")
        return False, 0, 0, []

# 新增：精确瞄准检查函数
def check_precision_targeting(target_x, target_y, center_x=400, center_y=240):
    """检查是否达到精确瞄准条件"""
    global precision_stable_count, precision_start_time, laser_enabled
    
    # 计算距离
    distance = math.sqrt((target_x - center_x) ** 2 + (target_y - center_y) ** 2)
    
    current_time = ticks_ms()
    
    if distance <= precision_threshold:
        if precision_stable_count == 0:
            precision_start_time = current_time
        
        precision_stable_count += 1
        
        # 检查是否在0.5秒内达到稳定
        if current_time - precision_start_time <= 500:  # 0.5秒 = 500ms
            if precision_stable_count >= 5:  # 连续5次检测稳定
                return True
        else:
            # 超时，重置计数器
            precision_stable_count = 0
            precision_start_time = current_time
    else:
        # 距离超出阈值，重置计数器
        precision_stable_count = 0
    
    return False

# 新增：激光笔控制函数
def fire_laser():
    """发射激光笔"""
    global laser_enabled, laser_fire_time
    
    try:
        # 发送激光发射信号给单片机
        n = 9 & 0xFF
        send_lst = [0x2c, 0x12, n, 0x5B]
        safe_uart_send(send_lst)
        
        laser_enabled = True
        laser_fire_time = ticks_ms()
        print("激光笔发射!")
        
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False

#Flag==1,2,3 - 黑色边框检测与跟踪
def goto_target_point_1():
    """黑色边框检测与跟踪"""
    global stable_count, last_target_time, precision_stable_count

    img = sensor.snapshot()

    # 检测黑色边框
    has_target, center_x, center_y, rectangles = detect_black_rectangles(img)

    if has_target and len(rectangles) >= 2:
        last_target_time = ticks_ms()

        # 绘制检测到的矩形
        for i, rect in enumerate(rectangles[-2:]):  # 只显示最大的两个
            color = (0, 255, 0) if i == 0 else (255, 255, 0)  # 绿色和黄色区分
            img.draw_rectangle(rect['blob'].rect(), color=color, thickness=2)
            img.draw_string_advanced(rect['center_x']-10, rect['center_y']-20, 14,
                                   f"R{i+1}", color=color)

        # 绘制目标中心点
        img.draw_cross(center_x, center_y, color=(255, 0, 0), size=10, thickness=3)
        img.draw_circle(center_x, center_y, 20, color=(255, 0, 0), thickness=2)

        # 计算误差
        e_x = 400 - center_x
        e_y = 240 - center_y
        distance = calculate_distance(center_x, center_y, 400, 240)

        # 检查精确瞄准条件
        if check_precision_targeting(center_x, center_y):
            # 达到精确瞄准条件，发射激光
            fire_laser()
            img.draw_string_advanced(10, 40, 20, "激光发射!", color=(255, 0, 0))
            precision_stable_count = 0  # 重置计数器
        else:
            # 发送控制信号
            e_x_high = (e_x >> 8) & 0xFF
            e_x_low = e_x & 0xFF
            e_y_high = (e_y >> 8) & 0xFF
            e_y_low = e_y & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

        # 显示信息
        img.draw_string_advanced(10, 10, 16, f"检测到{len(rectangles)}个矩形", color=(0, 255, 0))
        img.draw_string_advanced(10, 60, 16, f"目标中心: ({center_x}, {center_y})", color=(255, 255, 255))
        img.draw_string_advanced(10, 80, 16, f"距离中心: {distance:.1f}px", color=(255, 255, 255))
        img.draw_string_advanced(10, 100, 16, f"误差: X={e_x}, Y={e_y}", color=(255, 255, 255))
        img.draw_string_advanced(10, 120, 16, f"精确计数: {precision_stable_count}/5", color=(255, 255, 0))

    else:
        img.draw_string_advanced(10, 10, 20, "未检测到黑色边框", color=(255, 0, 0))
        img.draw_string_advanced(10, 40, 16, f"检测到{len(rectangles)}个候选区域", color=(255, 255, 0))
        img.draw_string_advanced(10, 60, 16, f"当前阈值: {current_black_threshold}", color=(255, 255, 0))

        # 显示所有候选区域
        for i, rect in enumerate(rectangles):
            img.draw_rectangle(rect['blob'].rect(), color=(100, 100, 100), thickness=1)

    # 绘制屏幕中心十字线
    img.draw_cross(400, 240, color=(0, 0, 255), size=15, thickness=2)
    img.draw_circle(400, 240, precision_threshold, color=(0, 0, 255), thickness=1)

    Display.show_image(img)

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数 - 完整的原版系统
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag, current_black_threshold

    # 捕获摄像头图像
    img_cam = sensor.snapshot()
    img_cam = img_cam.copy(roi=cut_roi)

    # 创建用于绘制按钮的画布
    img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

    # 应用当前阈值到裁剪的图像
    if current_mode == 'gray':
        # 灰度图识别 - 二值化显示
        img_processed = img_cam.to_grayscale()
        img_processed = img_processed.binary([gray_threshold[:2]])
        img_processed = img_processed.to_rgb565()
        # 同时更新黑色检测阈值
        current_black_threshold = tuple(gray_threshold[:2])
    else:  # 'lab'
        # LAB空间识别 - 二值化显示
        try:
            img_processed = img_cam.binary([lab_threshold])
            img_processed = img_processed.to_rgb565()
        except:
            img_processed = img_cam

    # 将处理后的图像绘制到中央
    center_x = (800 - img_processed.width()) // 2
    center_y = (480 - img_processed.height()) // 2
    img.draw_image(img_processed, center_x, center_y)

    # 绘制界面按钮
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)

    # 返回按钮
    img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 10, 20, "返回", color=text_color)

    # 切换按钮
    img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 10, 20, "切换", color=text_color)

    # 归位按钮
    img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 480-30, 20, "归位", color=text_color)

    # 保存按钮
    img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 480-30, 20, "保存", color=text_color)

    # 左侧滑块按钮（减少值）
    button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(0, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_left[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(70, y_pos + 10, 20, button_labels_left[i], color=text_color)

    # 右侧滑块按钮（增加值）
    button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(800-160, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_right[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(800-160+70, y_pos + 10, 20, button_labels_right[i], color=text_color)

    # 显示当前模式提示
    img.draw_rectangle(300, 10, 200, 30, color=(200, 200, 200), thickness=2, fill=True)
    mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
    img.draw_string_advanced(320, 15, 20, f"模式: {mode_text}", color=text_color)

    # 显示当前阈值值
    if current_mode == 'gray':
        img.draw_string_advanced(10, 420, 18,
                                 f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                 color=text_color)
    else:
        img.draw_string_advanced(10, 420, 16,
                                 f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                 color=text_color)

    # 处理触摸输入
    points = tp.read()
    if points:
        x, y = points[0].x, points[0].y

        # 判断按下的按钮
        def which_key(x, y):
            if x < 160:
                if y < 40: return "return"
                if y > 480-40: return "reset"
                if 60 <= y < 420: return str((y-60)//60)
            elif x > 800-160:
                if y < 40: return "change"
                if y > 480-40: return "save"
                if 60 <= y < 420: return str((y-60)//60+6)
            return None

        btn = which_key(x, y)
        if btn:
            # 返回按钮
            if btn == "return":
                threshold_adjustment_mode = False
                Flag = 0  # 返回到正常模式
                print("退出阈值调节模式，Flag重置为0")
                return img

            # 切换阈值模式
            elif btn == "change":
                if current_mode == 'lab':
                    current_mode = 'gray'
                    print("切换到灰度模式")
                else:
                    current_mode = 'lab'
                    print("切换到LAB模式")

            # 阈值归位
            elif btn == "reset":
                if current_mode == 'gray':
                    gray_threshold[0] = 0
                    gray_threshold[1] = 255
                    current_black_threshold = tuple(gray_threshold[:2])
                    print("已重置灰度阈值为全白色背景")
                else:  # lab
                    lab_threshold[0] = 0
                    lab_threshold[1] = 100
                    lab_threshold[2] = -128
                    lab_threshold[3] = 127
                    lab_threshold[4] = -128
                    lab_threshold[5] = 127
                    print("已重置LAB阈值为全白色背景")

            # 保存当前阈值
            elif btn == "save":
                if current_mode == 'lab':
                    # LAB模式保存时需要调整A、B通道的值
                    adj_vals = [lab_threshold[i] for i in range(6)]
                    saved_lab_thresholds.append(adj_vals.copy())
                    print(f"已保存LAB阈值: {adj_vals}")
                elif current_mode == 'gray':
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    current_black_threshold = tuple(gray_threshold[:2])
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            # 调整滑块
            else:
                idx = int(btn)
                max_params = 2 if current_mode == 'gray' else 6

                if idx >= 6:  # 右侧按钮增加阈值
                    chan_idx = idx - 6
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = min(255, gray_threshold[chan_idx] + 5)
                            current_black_threshold = tuple(gray_threshold[:2])
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = min(100, lab_threshold[chan_idx] + 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = min(127, lab_threshold[chan_idx] + 2)
                else:  # 左侧按钮减小阈值
                    chan_idx = idx
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = max(0, gray_threshold[chan_idx] - 5)
                            current_black_threshold = tuple(gray_threshold[:2])
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = max(0, lab_threshold[chan_idx] - 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = max(-128, lab_threshold[chan_idx] - 2)

                # 验证阈值逻辑
                validate_threshold()

            time.sleep_ms(100)  # 防止重复触发

    return img

#_____________________________________________主程序________________________________________________

while True:
    clock.tick()
    os.exitpoint()

    # 串口数据处理
    if uart.any():
        Flag_transform()

    # 实现一个长按屏幕进入阈值编辑模式的效果
    points = tp.read()
    if len(points) > 0:
        touch_counter += 1
        if touch_counter > 60:  # 大约3秒（假设20FPS，所以60帧约3秒）
            if not threshold_adjustment_mode:
                threshold_adjustment_mode = True
                Flag = 1
                # 重置阈值为全白色背景的初始值
                lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB全范围，显示纯白色
                gray_threshold = [0, 255]  # 灰度全范围，显示纯白色
                current_mode = 'lab'  # 默认进入LAB模式
                print("进入阈值调节模式 - 初始化为纯白色背景")
                touch_counter = 0  # 重置计数器
        print(f"触摸位置: {points[0].x}, {points[0].y}")
    else:
        touch_counter -= 2
        touch_counter = max(0, touch_counter)

    # 主要功能逻辑
    if Flag == 0:
        img = sensor.snapshot()
        Display.show_image(img) #显示图片

    elif Flag == 1:
        # 检查是否进入阈值调节模式
        if threshold_adjustment_mode:
            # 执行阈值调节系统
            img = threshold_adjustment_system()
            Display.show_image(img)
        else:
            # 如果没有进入阈值调节模式，显示黑色边框检测
            goto_target_point_1()

    elif Flag == 2:
        # 黑色边框检测功能
        goto_target_point_1()

    elif Flag == 3:
        # 增强版黑色边框检测
        goto_target_point_1()

    else:
        # 其他Flag值都使用黑色边框检测
        goto_target_point_1()
