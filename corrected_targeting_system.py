import time 
import os 
import sys 
import math 
import random 
import gc 
 
from time import ticks_ms 
from machine import FPIOA 
from machine import Pin 
from machine import PWM 
from machine import Timer 
from machine import UART 
from machine import RTC 
from machine import ADC 
from machine import TOUCH 
 
from media.sensor import * 
from media.display import * 
from media.media import * 
 
#______________________________________变量定义并赋初值___________________________________________

#任务标志位，通过串口接受数据改变
Flag = 2  # 设置为2进行测试

# Flag = 0   正常拍照
# Flag = 1   脱机调整阈值并且存储
# Flag = 2   基础部分第二题
# Flag = 3   基础部分第三题
# Flag = 4   发挥部分第一题
# Flag = 5   发挥部分第二题
# Flag = 6   发挥部分第三题
# Flag = 7   其他部分

# 新增：性能优化变量
max_frame_skip = 1  # 减少跳帧，提高检测精度
frame_skip_count = 0  # 跳帧计数

# 脱机调阈值系统的全局变量
threshold_adjustment_mode = False  # 是否进入阈值调节模式
current_mode = 'lab'  # 当前工作模式：'lab' 或 'gray'
lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB阈值
gray_threshold = [0, 255]  # 灰度阈值
saved_lab_thresholds = []  # 保存的LAB阈值列表
saved_gray_thresholds = []  # 保存的灰度阈值列表

#颜色阈值
blue_threshold = (30, 50, -50, -15, -10, 30)
binary_threshold = [92, 171]

#矩形中心的坐标
rect_cx = None
rect_cy = None

#矩形四点的坐标
rect_corner_points = []

#屏幕中心
target_2_cx = 400
target_2_cy = 240

#触摸次数
touch_counter = 0

# 图像裁剪ROI（用于阈值调节）
cut_roi = (160, 120, 480, 240)
 
#_________________________________________模块初始化_____________________________________________ 
 
#摄像头传感器初始化 
sensor = Sensor() 
sensor.reset() 
sensor.set_framesize(width = 800, height = 480) 
sensor.set_pixformat(Sensor.RGB565) 
 
#串口初始化 
fpioa = FPIOA() 
# UART1代码 
fpioa.set_function(3,FPIOA.UART1_TXD) 
fpioa.set_function(4,FPIOA.UART1_RXD) 
 
uart=UART(UART.UART1,115200) #设置串口号1和波特率 
 
fpioa.set_function(52,FPIOA.GPIO52) 
LED=Pin(52,Pin.OUT) #构建led对象，GPIO52,输出 
 
#缓冲区和3.5寸的屏幕图像显示 
Display.init(Display.ST7701, to_ide=True) 
 
# 初始化媒体管理器 
MediaManager.init() 
 
#启动摄像头传感器模块 
sensor.run() 
 
#时钟模块建立 
clock = time.clock() 
 
#建立触摸屏 
tp = TOUCH(0) 

print("矩形检测瞄准系统初始化完成")
 
#__________________________________________功能函数定义_____________________________________________ 
 
#找到对应最大色块 
def find_max_blob(blobs): 
    if not blobs:
        return None
    max_size = 0 
    max_blob = None 
    for b in blobs : 
        if b[2]*b[3] > max_size : 
            max_blob = b 
            max_size = b[2]*b[3] 
    return max_blob 
 
def find_max_rect(rects): 
    if not rects:
        return None
    max_size = 0 
    max_rect = None 
    for r in rects : 
        if r.w()*r.h() > max_size: 
            max_rect = r 
            max_size = r.w()*r.h() 
    return max_rect 

# 安全的串口发送函数
def safe_uart_send(data_list):
    """安全的串口发送函数，包含错误处理"""
    try:
        uart.write(bytes(data_list))
        return True
    except Exception as e:
        print(f"串口发送错误: {e}")
        return False

# 计算两点距离
def calculate_distance(x1, y1, x2, y2):
    """计算两点之间的距离"""
    return math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)

# 新增：精确瞄准检查函数
def check_precision_targeting(target_x, target_y, center_x=400, center_y=240):
    """检查是否达到精确瞄准条件"""
    global precision_stable_count, precision_start_time
    
    # 计算距离
    distance = math.sqrt((target_x - center_x) ** 2 + (target_y - center_y) ** 2)
    
    current_time = ticks_ms()
    
    if distance <= precision_threshold:
        if precision_stable_count == 0:
            precision_start_time = current_time
        
        precision_stable_count += 1
        
        # 检查是否在0.5秒内达到稳定
        if current_time - precision_start_time <= 500:  # 0.5秒 = 500ms
            if precision_stable_count >= 5:  # 连续5次检测稳定
                return True
        else:
            # 超时，重置计数器
            precision_stable_count = 0
            precision_start_time = current_time
    else:
        # 距离超出阈值，重置计数器
        precision_stable_count = 0
    
    return False

# 新增：激光笔控制函数
def fire_laser():
    """发射激光笔"""
    try:
        # 发送激光发射信号给单片机
        n = 9 & 0xFF
        send_lst = [0x2c, 0x12, n, 0x5B]
        safe_uart_send(send_lst)
        
        print("激光笔发射!")
        return True
    except Exception as e:
        print(f"激光发射错误: {e}")
        return False
 
#使用灰度阈值进行矩形检测 - 修复版本
def img_bin(img): 
    global rect_corner_points, binary_threshold
 
    # 转换到灰度图像进行矩形检测 
    img_gray = img.to_grayscale(copy=True) 
 
    # 使用保存的灰度阈值，如果没有则使用默认值 
    if saved_gray_thresholds: 
        binary_threshold = saved_gray_thresholds[-1]  # 使用最后保存的阈值 
    else: 
        binary_threshold = [92, 171]  # 默认阈值 
 
    # 应用二值化增强边缘 
    img_binary = img_gray.binary([binary_threshold]) 
    rects = img_binary.find_rects(threshold=10000) 
 
    # 清空前一次的角点 
    rect_corner_points = [] 
    if rects: 
        # 获取所有矩形的角点
        for rect in rects:
            corners = rect.corners()
            rect_corner_points.append(corners)
    
    return img_binary, rects

#任务标志位改变函数
def Flag_transform():
    global Flag, threshold_adjustment_mode
    try:
        # 读取3个字节
        data = uart.read()
        # 如果读取到数据且长度为3
        if data is not None and len(data) == 3:
            # 检查包头和包尾
            if data[0] == 0xAA and data[2] == 0xAB:
                Flag = data[1]
                print(f"Flag更新为: {Flag}")
    except Exception as e:
        print(f"串口数据处理错误: {e}")

#存储脱机调整的阈值
def storage():
    global current_blue_threshold, binary_threshold
    try:
        # 使用保存的LAB阈值，如果没有则使用默认值
        if saved_lab_thresholds:
            current_blue_threshold = tuple(saved_lab_thresholds[-1])   # 使用最后一个保存的LAB阈值作为蓝色
        else:
            current_blue_threshold = blue_threshold  # 使用默认蓝色阈值

        # 使用保存的灰度阈值更新二值化阈值
        if saved_gray_thresholds:
            binary_threshold = saved_gray_thresholds[-1]

        print(f"阈值已更新 - 蓝色: {current_blue_threshold}, 二值化: {binary_threshold}")
    except Exception as e:
        print(f"阈值存储错误: {e}")

#Flag==2 - 修复版本
def goto_target_point_1():
    global rect_cx, rect_cy, precision_stable_count

    img = sensor.snapshot()
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)
    img_binary, rects = img_bin(img)
    max_rect = find_max_rect(rects)

    if not blue_blobs:
        if max_rect:
            # 计算矩形中心
            rect_cx = sum(p[0] for p in max_rect.corners()) / 4
            rect_cy = sum(p[1] for p in max_rect.corners()) / 4

            # 绘制矩形和中心点
            img.draw_circle(int(rect_cx), int(rect_cy), 3, color=(255, 0, 0), thickness=2, fill=True)

            # 绘制矩形轮廓
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            # 计算误差
            e_x_2 = 400 - rect_cx
            e_y_2 = 240 - rect_cy
            distance = calculate_distance(rect_cx, rect_cy, 400, 240)

            # 检查精确瞄准条件
            if check_precision_targeting(rect_cx, rect_cy):
                # 达到精确瞄准条件，发射激光
                fire_laser()
                img.draw_string_advanced(10, 40, 20, "激光发射!", color=(255, 0, 0))
                precision_stable_count = 0  # 重置计数器
            else:
                # 发送控制信号
                e_x_2_high = (int(e_x_2) >> 8) & 0xFF
                e_x_2_low = int(e_x_2) & 0xFF
                e_y_2_high = (int(e_y_2) >> 8) & 0xFF
                e_y_2_low = int(e_y_2) & 0xFF

                send_lst = [0x2c, 0x12, e_x_2_high, e_x_2_low, e_y_2_high, e_y_2_low, 0x5B]
                safe_uart_send(send_lst)

            # 显示信息
            img.draw_string_advanced(10, 10, 16, f"矩形中心: ({int(rect_cx)}, {int(rect_cy)})", color=(255, 255, 255))
            img.draw_string_advanced(10, 60, 16, f"距离中心: {distance:.1f}px", color=(255, 255, 255))
            img.draw_string_advanced(10, 80, 16, f"误差: X={int(e_x_2)}, Y={int(e_y_2)}", color=(255, 255, 255))
            img.draw_string_advanced(10, 100, 16, f"精确计数: {precision_stable_count}/5", color=(255, 255, 0))
        else:
            img.draw_string_advanced(10, 10, 20, "未检测到矩形", color=(255, 0, 0))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob and max_rect:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            # 计算矩形中心
            rect_cx = sum(p[0] for p in max_rect.corners()) / 4
            rect_cy = sum(p[1] for p in max_rect.corners()) / 4

            e_x_3 = rect_cx - max_blue_blob.cx()
            e_y_3 = rect_cy - max_blue_blob.cy()

            e_x_3_high = (int(e_x_3) >> 8) & 0xFF
            e_x_3_low = int(e_x_3) & 0xFF
            e_y_3_high = (int(e_y_3) >> 8) & 0xFF
            e_y_3_low = int(e_y_3) & 0xFF

            send_lst = [0x2c, 0x12, e_x_3_high, e_x_3_low, e_y_3_high, e_y_3_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"相对跟踪模式", color=(255, 255, 0))

    # 绘制屏幕中心十字线
    img.draw_cross(400, 240, color=(0, 0, 255), size=15, thickness=2)
    img.draw_circle(400, 240, precision_threshold, color=(0, 0, 255), thickness=1)

    Display.show_image(img)

#Flag==3 - 修复版本
def goto_target_point_2():
    global rect_cx, rect_cy, precision_stable_count

    img = sensor.snapshot()
    img_binary, rects = img_bin(img)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    if not blue_blobs:
        if not rects:
            e_x_4 = target_1_cx - target_2_cx
            e_y_4 = target_1_cy - target_2_cy

            e_x_4_high = (e_x_4 >> 8) & 0xFF
            e_x_4_low = e_x_4 & 0xFF
            e_y_4_high = (e_y_4 >> 8) & 0xFF
            e_y_4_low = e_y_4 & 0xFF

            send_lst = [0x2c, 0x12, e_x_4_high, e_x_4_low, e_y_4_high, e_y_4_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, "移动到预设位置", color=(255, 255, 0))
        else:
            max_rect = find_max_rect(rects)
            if max_rect:
                rect_cx = sum(p[0] for p in max_rect.corners()) / 4
                rect_cy = sum(p[1] for p in max_rect.corners()) / 4
                img.draw_circle(int(rect_cx), int(rect_cy), 3, color=(255, 0, 0), thickness=2, fill=True)

                # 计算误差
                e_x_5 = 400 - rect_cx
                e_y_5 = 240 - rect_cy
                distance = calculate_distance(rect_cx, rect_cy, 400, 240)

                # 检查精确瞄准条件
                if check_precision_targeting(rect_cx, rect_cy):
                    # 达到精确瞄准条件，发射激光
                    fire_laser()
                    img.draw_string_advanced(10, 40, 20, "激光发射!", color=(255, 0, 0))
                    precision_stable_count = 0  # 重置计数器
                else:
                    # 发送控制信号
                    e_x_5_high = (int(e_x_5) >> 8) & 0xFF
                    e_x_5_low = int(e_x_5) & 0xFF
                    e_y_5_high = (int(e_y_5) >> 8) & 0xFF
                    e_y_5_low = int(e_y_5) & 0xFF

                    send_lst = [0x2c, 0x12, e_x_5_high, e_x_5_low, e_y_5_high, e_y_5_low, 0x5B]
                    safe_uart_send(send_lst)

                # 显示信息
                img.draw_string_advanced(10, 10, 16, f"矩形跟踪: ({int(rect_cx)}, {int(rect_cy)})", color=(0, 255, 0))
                img.draw_string_advanced(10, 60, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
                img.draw_string_advanced(10, 80, 16, f"精确计数: {precision_stable_count}/5", color=(255, 255, 0))
    else:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            if rects:
                max_rect = find_max_rect(rects)
                if max_rect:
                    rect_cx = sum(p[0] for p in max_rect.corners()) / 4
                    rect_cy = sum(p[1] for p in max_rect.corners()) / 4

                    e_x_6 = rect_cx - max_blue_blob.cx()
                    e_y_6 = rect_cy - max_blue_blob.cy()

                    e_x_6_high = (int(e_x_6) >> 8) & 0xFF
                    e_x_6_low = int(e_x_6) & 0xFF
                    e_y_6_high = (int(e_y_6) >> 8) & 0xFF
                    e_y_6_low = int(e_y_6) & 0xFF

                    send_lst = [0x2c, 0x12, e_x_6_high, e_x_6_low, e_y_6_high, e_y_6_low, 0x5B]
                    safe_uart_send(send_lst)

                    img.draw_string_advanced(10, 10, 16, "双目标相对跟踪", color=(255, 255, 0))

    # 绘制屏幕中心
    img.draw_cross(400, 240, color=(0, 0, 255), size=15, thickness=2)
    img.draw_circle(400, 240, precision_threshold, color=(0, 0, 255), thickness=1)

    Display.show_image(img)

#Flag==4,5 - 修复版本
def goto_target_point_3():
    global rect_cx, rect_cy

    img = sensor.snapshot()
    img_binary, rects = img_bin(img)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    if rects and blue_blobs:
        max_rect = find_max_rect(rects)
        max_blue_blob = find_max_blob(blue_blobs)

        if max_rect and max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=2)

            rect_cx = sum(p[0] for p in max_rect.corners()) / 4
            rect_cy = sum(p[1] for p in max_rect.corners()) / 4

            # 绘制矩形轮廓
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=2)

            img.draw_circle(int(rect_cx), int(rect_cy), 5, color=(255, 0, 0), thickness=2, fill=True)

            e_x_7 = rect_cx - max_blue_blob.cx()
            e_y_7 = rect_cy - max_blue_blob.cy()

            e_x_7_high = (int(e_x_7) >> 8) & 0xFF
            e_x_7_low = int(e_x_7) & 0xFF
            e_y_7_high = (int(e_y_7) >> 8) & 0xFF
            e_y_7_low = int(e_y_7) & 0xFF

            send_lst = [0x2c, 0x12, e_x_7_high, e_x_7_low, e_y_7_high, e_y_7_low, 0x5B]
            safe_uart_send(send_lst)

            # 显示详细信息
            distance = calculate_distance(rect_cx, rect_cy, max_blue_blob.cx(), max_blue_blob.cy())
            img.draw_string_advanced(10, 10, 16, f"高精度跟踪", color=(255, 255, 255))
            img.draw_string_advanced(10, 30, 16, f"矩形: ({int(rect_cx)}, {int(rect_cy)})", color=(0, 255, 0))
            img.draw_string_advanced(10, 50, 16, f"蓝色: ({max_blue_blob.cx()}, {max_blue_blob.cy()})", color=(255, 255, 0))
            img.draw_string_advanced(10, 70, 16, f"距离: {distance:.1f}px", color=(255, 255, 255))
    else:
        img.draw_string_advanced(10, 10, 20, "等待双目标出现...", color=(255, 0, 0))

    Display.show_image(img)

#Flag==6 - 新增功能
def goto_target_point_4():
    """多目标优先级跟踪"""
    img = sensor.snapshot()
    img_binary, rects = img_bin(img)
    blue_blobs = img.find_blobs([current_blue_threshold], pixels_threshold=10, area_threshold=10)

    # 优先跟踪矩形目标
    if rects:
        max_rect = find_max_rect(rects)
        if max_rect:
            rect_cx = sum(p[0] for p in max_rect.corners()) / 4
            rect_cy = sum(p[1] for p in max_rect.corners()) / 4

            # 绘制矩形
            corners = max_rect.corners()
            for i in range(4):
                start_idx = i
                end_idx = (i + 1) % 4
                img.draw_line(corners[start_idx][0], corners[start_idx][1],
                             corners[end_idx][0], corners[end_idx][1],
                             color=(0, 255, 0), thickness=3)

            img.draw_circle(int(rect_cx), int(rect_cy), 8, color=(255, 0, 0), thickness=3, fill=True)

            e_x = 400 - rect_cx
            e_y = 240 - rect_cy

            e_x_high = (int(e_x) >> 8) & 0xFF
            e_x_low = int(e_x) & 0xFF
            e_y_high = (int(e_y) >> 8) & 0xFF
            e_y_low = int(e_y) & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"优先跟踪矩形", color=(0, 255, 0))
            img.draw_string_advanced(10, 30, 16, f"中心: ({int(rect_cx)}, {int(rect_cy)})", color=(255, 255, 255))
    elif blue_blobs:
        max_blue_blob = find_max_blob(blue_blobs)
        if max_blue_blob:
            img.draw_rectangle(max_blue_blob.rect(), color=(255, 255, 0), thickness=3)

            e_x = 400 - max_blue_blob.cx()
            e_y = 240 - max_blue_blob.cy()

            e_x_high = (e_x >> 8) & 0xFF
            e_x_low = e_x & 0xFF
            e_y_high = (e_y >> 8) & 0xFF
            e_y_low = e_y & 0xFF

            send_lst = [0x2c, 0x12, e_x_high, e_x_low, e_y_high, e_y_low, 0x5B]
            safe_uart_send(send_lst)

            img.draw_string_advanced(10, 10, 16, f"跟踪蓝色目标", color=(255, 255, 0))
    else:
        img.draw_string_advanced(10, 10, 20, "未检测到任何目标", color=(255, 0, 0))

    Display.show_image(img)

#Flag==7 - 系统监控
def goto_target_point_5():
    """系统状态监控"""
    img = sensor.snapshot()

    # 显示系统信息
    fps = clock.fps()
    gc.collect()
    free_mem = gc.mem_free()

    img.draw_rectangle(10, 10, 300, 150, color=(50, 50, 50), thickness=2, fill=True)
    img.draw_string_advanced(20, 20, 20, "系统监控", color=(255, 255, 255))
    img.draw_string_advanced(20, 50, 16, f"FPS: {fps:.1f}", color=(0, 255, 0))
    img.draw_string_advanced(20, 70, 16, f"内存: {free_mem//1024}KB", color=(255, 255, 0))
    img.draw_string_advanced(20, 90, 16, f"Flag: {Flag}", color=(255, 255, 255))
    img.draw_string_advanced(20, 110, 14, f"蓝色阈值: {current_blue_threshold}", color=(0, 0, 255))
    img.draw_string_advanced(20, 130, 14, f"二值化阈值: {binary_threshold}", color=(255, 255, 255))

    Display.show_image(img)

#_________________________________________脱机调阈值系统函数_____________________________________________

# 阈值验证函数
def validate_threshold():
    """验证阈值的逻辑正确性，确保min值不大于max值"""
    if current_mode == 'lab':
        # 检查L_min <= L_max
        if lab_threshold[0] > lab_threshold[1]:
            lab_threshold[0] = lab_threshold[1]
        # 检查A_min <= A_max
        if lab_threshold[2] > lab_threshold[3]:
            lab_threshold[2] = lab_threshold[3]
        # 检查B_min <= B_max
        if lab_threshold[4] > lab_threshold[5]:
            lab_threshold[4] = lab_threshold[5]
    else:  # gray mode
        # 检查gray_min <= gray_max
        if gray_threshold[0] > gray_threshold[1]:
            gray_threshold[0] = gray_threshold[1]

# 阈值调整系统主函数
def threshold_adjustment_system():
    """
    脱机调阈值系统主函数
    返回处理后的图像，由主程序负责显示
    """
    global current_mode, threshold_adjustment_mode, Flag, binary_threshold

    # 捕获摄像头图像
    img_cam = sensor.snapshot()
    img_cam = img_cam.copy(roi=cut_roi)

    # 创建用于绘制按钮的画布
    img = sensor.snapshot()  # 使用完整的摄像头图像作为背景

    # 应用当前阈值到裁剪的图像
    if current_mode == 'gray':
        # 灰度图识别 - 二值化显示
        img_processed = img_cam.to_grayscale()
        img_processed = img_processed.binary([gray_threshold[:2]])
        img_processed = img_processed.to_rgb565()
        # 同时更新二值化阈值
        binary_threshold = gray_threshold[:2]
    else:  # 'lab'
        # LAB空间识别 - 二值化显示
        try:
            img_processed = img_cam.binary([lab_threshold])
            img_processed = img_processed.to_rgb565()
        except:
            img_processed = img_cam

    # 将处理后的图像绘制到中央
    center_x = (800 - img_processed.width()) // 2
    center_y = (480 - img_processed.height()) // 2
    img.draw_image(img_processed, center_x, center_y)

    # 绘制界面按钮
    button_color = (150, 150, 150)
    text_color = (0, 0, 0)

    # 返回按钮
    img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 10, 20, "返回", color=text_color)

    # 切换按钮
    img.draw_rectangle(800-160, 0, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 10, 20, "切换", color=text_color)

    # 归位按钮
    img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(50, 480-30, 20, "归位", color=text_color)

    # 保存按钮
    img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
    img.draw_string_advanced(800-160+50, 480-30, 20, "保存", color=text_color)

    # 左侧滑块按钮（减少值）
    button_labels_left = ["L-", "L-", "A-", "A-", "B-", "B-"] if current_mode == 'lab' else ["G-", "G-", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(0, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_left[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(70, y_pos + 10, 20, button_labels_left[i], color=text_color)

    # 右侧滑块按钮（增加值）
    button_labels_right = ["L+", "L+", "A+", "A+", "B+", "B+"] if current_mode == 'lab' else ["G+", "G+", "", "", "", ""]
    for i in range(6):
        y_pos = 60 + i * 60
        img.draw_rectangle(800-160, y_pos, 160, 40, color=button_color, thickness=2, fill=True)
        if button_labels_right[i]:  # 只显示有效按钮的标签
            img.draw_string_advanced(800-160+70, y_pos + 10, 20, button_labels_right[i], color=text_color)

    # 显示当前模式提示
    img.draw_rectangle(300, 10, 200, 30, color=(200, 200, 200), thickness=2, fill=True)
    mode_text = "灰度模式" if current_mode == 'gray' else "LAB模式"
    img.draw_string_advanced(320, 15, 20, f"模式: {mode_text}", color=text_color)

    # 显示当前阈值值
    if current_mode == 'gray':
        img.draw_string_advanced(10, 420, 18,
                                 f"灰度阈值: [{gray_threshold[0]}, {gray_threshold[1]}]",
                                 color=text_color)
    else:
        img.draw_string_advanced(10, 420, 16,
                                 f"LAB阈值: L[{lab_threshold[0]},{lab_threshold[1]}] A[{lab_threshold[2]},{lab_threshold[3]}] B[{lab_threshold[4]},{lab_threshold[5]}]",
                                 color=text_color)

    # 处理触摸输入
    points = tp.read()
    if points:
        x, y = points[0].x, points[0].y

        # 判断按下的按钮
        def which_key(x, y):
            if x < 160:
                if y < 40: return "return"
                if y > 480-40: return "reset"
                if 60 <= y < 420: return str((y-60)//60)
            elif x > 800-160:
                if y < 40: return "change"
                if y > 480-40: return "save"
                if 60 <= y < 420: return str((y-60)//60+6)
            return None

        btn = which_key(x, y)
        if btn:
            # 返回按钮
            if btn == "return":
                threshold_adjustment_mode = False
                Flag = 0  # 返回到正常模式
                print("退出阈值调节模式，Flag重置为0")
                return img

            # 切换阈值模式
            elif btn == "change":
                if current_mode == 'lab':
                    current_mode = 'gray'
                    print("切换到灰度模式")
                else:
                    current_mode = 'lab'
                    print("切换到LAB模式")

            # 阈值归位
            elif btn == "reset":
                if current_mode == 'gray':
                    gray_threshold[0] = 0
                    gray_threshold[1] = 255
                    binary_threshold = gray_threshold[:2]
                    print("已重置灰度阈值为全白色背景")
                else:  # lab
                    lab_threshold[0] = 0
                    lab_threshold[1] = 100
                    lab_threshold[2] = -128
                    lab_threshold[3] = 127
                    lab_threshold[4] = -128
                    lab_threshold[5] = 127
                    print("已重置LAB阈值为全白色背景")

            # 保存当前阈值
            elif btn == "save":
                if current_mode == 'lab':
                    # LAB模式保存时需要调整A、B通道的值（减去128）
                    adj_vals = [lab_threshold[i] for i in range(6)]
                    saved_lab_thresholds.append(adj_vals.copy())
                    print(f"已保存LAB阈值: {adj_vals}")
                elif current_mode == 'gray':
                    saved_gray_thresholds.append(gray_threshold[:2].copy())
                    binary_threshold = gray_threshold[:2]
                    print(f"已保存灰度阈值: {gray_threshold[:2]}")

            # 调整滑块
            else:
                idx = int(btn)
                max_params = 2 if current_mode == 'gray' else 6

                if idx >= 6:  # 右侧按钮增加阈值
                    chan_idx = idx - 6
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = min(255, gray_threshold[chan_idx] + 5)
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = min(100, lab_threshold[chan_idx] + 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = min(127, lab_threshold[chan_idx] + 2)
                else:  # 左侧按钮减小阈值
                    chan_idx = idx
                    if chan_idx < max_params:
                        if current_mode == 'gray':
                            gray_threshold[chan_idx] = max(0, gray_threshold[chan_idx] - 5)
                            binary_threshold = gray_threshold[:2]
                        else:  # lab
                            if chan_idx < 2:  # L通道
                                lab_threshold[chan_idx] = max(0, lab_threshold[chan_idx] - 2)
                            else:  # A, B通道
                                lab_threshold[chan_idx] = max(-128, lab_threshold[chan_idx] - 2)

                # 验证阈值逻辑
                validate_threshold()

            time.sleep_ms(100)  # 防止重复触发

    return img

#_____________________________________________主程序________________________________________________
while True:
    clock.tick()
    os.exitpoint()

    # 串口数据处理
    if uart.any():
        Flag_transform()

    # 实现一个长按屏幕进入阈值编辑模式的效果
    points = tp.read()
    if len(points) > 0:
        touch_counter += 1
        if touch_counter > 60:  # 大约3秒（假设20FPS，所以60帧约3秒）
            if not threshold_adjustment_mode:
                threshold_adjustment_mode = True
                Flag = 1
                # 重置阈值为全白色背景的初始值
                lab_threshold = [0, 100, -128, 127, -128, 127]  # LAB全范围，显示纯白色
                gray_threshold = [0, 255]  # 灰度全范围，显示纯白色
                current_mode = 'lab'  # 默认进入LAB模式
                print("进入阈值调节模式 - 初始化为纯白色背景")
                touch_counter = 0  # 重置计数器
        print(f"触摸位置: {points[0].x}, {points[0].y}")
    else:
        touch_counter -= 2
        touch_counter = max(0, touch_counter)

    # 主要功能逻辑
    if Flag == 0:
        img = sensor.snapshot()
        Display.show_image(img) #显示图片

    elif Flag == 1:
        # 检查是否进入阈值调节模式
        if threshold_adjustment_mode:
            # 执行阈值调节系统
            img = threshold_adjustment_system()
            storage()  # 修复：去掉多余的冒号
            Display.show_image(img)
        else:
            # 如果没有进入阈值调节模式，显示矩形检测
            goto_target_point_1()

#基础部分第二题
    elif Flag == 2:
        goto_target_point_1()

#基础部分第三题
    elif Flag == 3:
        goto_target_point_2()

#发挥部分第一题
    elif Flag == 4:
        goto_target_point_3()

#发挥部分第二题
    elif Flag == 5:
        goto_target_point_3()

#发挥部分第三题
    elif Flag == 6:
        goto_target_point_4()

#其他加分项
    elif Flag == 7:
        goto_target_point_5()
